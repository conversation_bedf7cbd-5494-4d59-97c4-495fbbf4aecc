import SwiftUI

struct MenuBarView: View {
    @EnvironmentObject private var appViewModel: AppViewModel
    @Environment(\.openWindow) private var openWindow
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            headerSection
            
            Divider()
            
            // Status Section
            statusSection
            
            Divider()
            
            // Controls Section
            controlsSection
            
            Divider()
            
            // Actions Section
            actionsSection
        }
        .padding()
        .frame(width: 280)
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        HStack {
            Image(systemName: "doc.on.clipboard")
                .font(.title2)
                .foregroundColor(.blue)
            
            VStack(alignment: .leading, spacing: 2) {
                Text("NtfyClip")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Text("Clipboard Sync")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            connectionStatusIndicator
        }
    }
    
    // MARK: - Connection Status Indicator
    private var connectionStatusIndicator: some View {
        Circle()
            .fill(connectionStatusColor)
            .frame(width: 8, height: 8)
            .overlay(
                Circle()
                    .stroke(connectionStatusColor.opacity(0.3), lineWidth: 2)
                    .scaleEffect(appViewModel.isConnected ? 1.5 : 1.0)
                    .opacity(appViewModel.isConnected ? 0 : 1)
                    .animation(.easeInOut(duration: 1.0).repeatForever(autoreverses: false), 
                              value: appViewModel.isConnected)
            )
    }
    
    private var connectionStatusColor: Color {
        if appViewModel.errorMessage != nil {
            return .red
        } else if appViewModel.isConnected {
            return .green
        } else if appViewModel.isSyncEnabled {
            return .orange
        } else {
            return .gray
        }
    }
    
    // MARK: - Status Section
    private var statusSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Status:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(statusText)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(statusTextColor)
            }
            
            if let lastSyncTime = appViewModel.lastSyncTime {
                HStack {
                    Text("Last Sync:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(lastSyncTime, style: .relative)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            HStack {
                Text("Items Synced:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text("\(appViewModel.clipboardItemsCount)")
                    .font(.caption)
                    .fontWeight(.medium)
            }
            
            // Error message if present
            if let errorMessage = appViewModel.errorMessage {
                HStack(alignment: .top, spacing: 6) {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .foregroundColor(.red)
                        .font(.caption)
                    
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.leading)
                    
                    Spacer()
                    
                    Button("Dismiss") {
                        appViewModel.clearError()
                    }
                    .font(.caption)
                    .buttonStyle(.borderless)
                }
                .padding(.vertical, 4)
                .padding(.horizontal, 8)
                .background(Color.red.opacity(0.1))
                .cornerRadius(6)
            }
        }
    }
    
    private var statusText: String {
        if appViewModel.errorMessage != nil {
            return "Error"
        } else if appViewModel.isConnected {
            return "Connected"
        } else if appViewModel.isSyncEnabled {
            return "Connecting..."
        } else {
            return "Disabled"
        }
    }
    
    private var statusTextColor: Color {
        if appViewModel.errorMessage != nil {
            return .red
        } else if appViewModel.isConnected {
            return .green
        } else if appViewModel.isSyncEnabled {
            return .orange
        } else {
            return .secondary
        }
    }
    
    // MARK: - Controls Section
    private var controlsSection: some View {
        VStack(spacing: 12) {
            Button(action: {
                appViewModel.toggleSync()
            }) {
                HStack(spacing: 10) {
                    Image(systemName: appViewModel.isSyncEnabled ? "stop.circle.fill" : "play.circle.fill")
                        .foregroundColor(appViewModel.isSyncEnabled ? .red : .green)
                        .font(.system(size: 16, weight: .medium))
                    Text(appViewModel.isSyncEnabled ? "Stop Sync" : "Start Sync")
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.regularMaterial)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.secondary.opacity(0.2), lineWidth: 1)
                        )
                )
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(appViewModel.errorMessage != nil)
        }
    }
    
    // MARK: - Actions Section
    private var actionsSection: some View {
        VStack(spacing: 8) {
            Button(action: {
                openWindow(id: "settings")
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "gearshape")
                        .font(.system(size: 14, weight: .medium))
                    Text("Settings")
                        .fontWeight(.medium)
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(.ultraThinMaterial)
                )
            }
            .buttonStyle(PlainButtonStyle())

            Button(action: {
                NSApplication.shared.terminate(nil)
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "power")
                        .font(.system(size: 14, weight: .medium))
                    Text("Quit NtfyClip")
                        .fontWeight(.medium)
                    Spacer()
                }
                .foregroundColor(.red)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(.ultraThinMaterial)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
    }
}
